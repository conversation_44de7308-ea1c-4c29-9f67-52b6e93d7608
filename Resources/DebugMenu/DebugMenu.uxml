<ui:UXML xmlns:ui="UnityEngine.UIElements" xmlns:uie="UnityEditor.UIElements" editor-extension-mode="False">
    <Style src="project://database/Assets/Plugins/DebugMenu/Resources/DebugMenu/Style.uss?fileID=7433441132597879392&amp;guid=67b278399d89b3140ba6bff7604d57f3&amp;type=3#Style" />
    <ui:VisualElement name="Root" class="root" style="flex-grow: 1; background-color: rgba(0, 0, 0, 0.72); padding-top: 10px; padding-right: 10px; padding-bottom: 10px; padding-left: 10px; position: absolute; left: 0; top: 0; right: 0; bottom: 0;">
        <ui:VisualElement name="Header" style="flex-grow: 1; flex-direction: row; height: 16px; max-height: 38px; border-left-color: rgb(255, 255, 255); border-right-color: rgb(255, 255, 255); border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); border-bottom-width: 1px;">
            <ui:Label tabindex="-1" text="Debug Menu" parse-escape-sequences="true" display-tooltip-when-elided="true" name="Title" style="color: rgb(255, 255, 255); -unity-font-definition: initial; -unity-font: url(&apos;project://database/Assets/Plugins/DebugMenu/Font/monogram.ttf?fileID=12800000&amp;guid=fbfd8518994a56a47a82ba267da8c831&amp;type=3#monogram&apos;); font-size: 32px; border-bottom-width: 0; border-left-color: rgb(255, 255, 255); border-right-color: rgb(255, 255, 255); border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); margin-bottom: 5px;" />
            <ui:Label tabindex="-1" text="root" parse-escape-sequences="true" display-tooltip-when-elided="true" name="Path" style="color: rgb(255, 255, 255); -unity-font-definition: initial; -unity-font: url(&apos;project://database/Assets/Plugins/DebugMenu/Font/monogram.ttf?fileID=12800000&amp;guid=fbfd8518994a56a47a82ba267da8c831&amp;type=3#monogram&apos;); font-size: 32px; border-bottom-width: 0; border-left-color: rgb(255, 255, 255); border-right-color: rgb(255, 255, 255); border-top-color: rgb(255, 255, 255); border-bottom-color: rgb(255, 255, 255); margin-bottom: 5px; border-left-width: 0;" />
        </ui:VisualElement>
        <ui:VisualElement name="Items" style="flex-grow: 1; flex-wrap: wrap; background-color: rgba(0, 0, 0, 0); padding-top: 3px;" />
    </ui:VisualElement>
</ui:UXML>
