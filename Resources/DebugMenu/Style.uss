.menuitem {
    background-color: rgba(0, 0, 0, 0);
    font-size: 32px;
    color: rgb(255, 255, 255);
    height: 18px;
    -unity-text-align: middle-left;
    white-space: normal;
    text-overflow: clip;
    -unity-font-definition: initial;
    -unity-font: url('project://database/Assets/DebugMenu/Font/monogram.ttf?fileID=12800000&guid=fbfd8518994a56a47a82ba267da8c831&type=3#monogram');
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    border-left-width: 0;
    padding-top: 0;
    padding-right: 0;
    padding-bottom: 4px;
    padding-left: 0;
}

.menuitem:focus {
    color: rgb(161, 201, 53);
    border-top-width: 0;
    border-right-width: 0;
    border-bottom-width: 0;
    border-left-width: 0;
    border-left-color: rgb(161, 201, 53);
    border-right-color: rgb(161, 201, 53);
    border-top-color: rgb(161, 201, 53);
    border-bottom-color: rgb(161, 201, 53);
    -unity-font-style: normal;
}

.unity-ui-document__root {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}