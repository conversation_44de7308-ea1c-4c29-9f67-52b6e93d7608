using System;
using UnityEngine;
using Utils;

public class DebugMenuDemo : MonoBehaviour
{
    [SerializeField] private IntValueHolder intValueHolder = new();
    [SerializeField] private BoolValueHolder boolValueHolder = new();

    private void Start()
    {
        DebugMenu.Instance.AddValueSwitcher("IntValue", intValueHolder.GetValue, intValueHolder.NextValue, intValueHolder.PreviousValue);
        DebugMenu.Instance.AddValueSwitcher("bool values/important/BoolValue", boolValueHolder.GetValue, boolValueHolder.SwitchValue);
        DebugMenu.Instance.AddAction("actions/boogaga", () => { Debug.Log("BOOGAGA"); });
    }

    void Update()
    {
        if (Input.GetKeyDown(KeyCode.End))
        {
            DebugMenu.Instance.Toggle();
        }
    }
}

[Serializable]
public class IntValueHolder
{
    [SerializeField] private int value;

    public object GetValue() => value;

    public void PreviousValue()
    {
        value--;
    }

    public void NextValue()
    {
        value++;
    }
}

[Serializable]
public class BoolValueHolder
{
    public bool value;

    public object GetValue() => value;

    public void SwitchValue()
    {
        value = !value;
    }
}